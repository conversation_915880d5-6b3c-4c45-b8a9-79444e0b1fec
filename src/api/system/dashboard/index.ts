import request from '@/config/axios'

// 统计数据接口类型定义
export interface DashboardStatsVO {
  orderCount: number
  dispatchCount: number
  completedCount: number
  totalAmount: number
}

export interface CompareStatsVO {
  dayCompare: string
  dispatchCompare: string
  mergeAmount: number
}

export interface PendingStatsVO {
  dayPending: string
  dispatchPending: string
  pendingAmount: number
}

export interface MonthStatsVO {
  monthCompare: string
  monthDispatch: string
  monthAmount: number
}

export interface TargetDataVO {
  label: string
  value: string
  unit: string
  color: string
}

export interface TableDataVO {
  id: number
  platform: string
  totalOrders: string
  dayCompare: string
  yearCompare: string
  operationDept: string
  operationOrders: string
  operationDayCompare: string
  operationYearCompare: string
  businessDept: string
  businessOrders: string
  businessDayCompare: string
  businessYearCompare: string
}

export interface DashboardQueryParams {
  pageNo: number
  pageSize: number
  dateRange?: string[]
  platform?: string
  deptType?: string
}

// 仪表板 API
export const DashboardApi = {
  // 获取今日统计数据
  getTodayStats: async (): Promise<DashboardStatsVO> => {
    return await request.get({ url: '/system/dashboard/today-stats' })
  },

  // 获取对比统计数据
  getCompareStats: async (): Promise<CompareStatsVO> => {
    return await request.get({ url: '/system/dashboard/compare-stats' })
  },

  // 获取待处理统计数据
  getPendingStats: async (): Promise<PendingStatsVO> => {
    return await request.get({ url: '/system/dashboard/pending-stats' })
  },

  // 获取月度统计数据
  getMonthStats: async (): Promise<MonthStatsVO> => {
    return await request.get({ url: '/system/dashboard/month-stats' })
  },

  // 获取运营目标数据
  getOperationTargetData: async (): Promise<TargetDataVO[]> => {
    return await request.get({ url: '/system/dashboard/operation-target' })
  },

  // 获取品牌目标数据
  getBrandTargetData: async (): Promise<TargetDataVO[]> => {
    return await request.get({ url: '/system/dashboard/brand-target' })
  },

  // 获取表格数据分页
  getTableDataPage: async (params: DashboardQueryParams) => {
    return await request.get({ url: '/system/dashboard/table-data', params })
  },

  // 刷新仪表板数据
  refreshDashboardData: async () => {
    return await request.post({ url: '/system/dashboard/refresh' })
  },

  // 导出仪表板数据
  exportDashboardData: async (params: DashboardQueryParams) => {
    return await request.download({ url: '/system/dashboard/export', params })
  }
}
