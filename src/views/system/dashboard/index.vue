<template>
  <div class="dashboard-container">
    <!-- 顶部统计卡片 -->
    <ContentWrap>
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-4">
        <!-- 今日数据 -->
        <div class="bg-white rounded-lg p-4 shadow-sm border">
          <div class="text-sm text-gray-500 mb-2">今日/日</div>
          <div class="grid grid-cols-3 gap-2 text-center">
            <div>
              <div class="text-xs text-gray-400">接单数</div>
              <div class="text-lg font-bold">{{ todayStats.orderCount || 0 }}</div>
            </div>
            <div>
              <div class="text-xs text-gray-400">派单数</div>
              <div class="text-lg font-bold">{{ todayStats.dispatchCount || '-' }}</div>
            </div>
            <div>
              <div class="text-xs text-gray-400">完成订单</div>
              <div class="text-lg font-bold">{{ todayStats.completedCount || '-' }}</div>
            </div>
          </div>
          <div class="mt-2 text-center">
            <div class="text-xs text-gray-400">合计金额</div>
            <div class="text-lg font-bold text-blue-600">{{ todayStats.totalAmount || 0 }}</div>
          </div>
        </div>

        <!-- 并升日数据 -->
        <div class="bg-white rounded-lg p-4 shadow-sm border">
          <div class="text-sm text-gray-500 mb-2">并升日</div>
          <div class="grid grid-cols-3 gap-2 text-center">
            <div>
              <div class="text-xs text-gray-400">并升日</div>
              <div class="text-lg font-bold">{{ compareStats.dayCompare || '-' }}</div>
            </div>
            <div>
              <div class="text-xs text-gray-400">派升日</div>
              <div class="text-lg font-bold">{{ compareStats.dispatchCompare || '-' }}</div>
            </div>
            <div>
              <div class="text-xs text-gray-400">合并金额</div>
              <div class="text-lg font-bold">{{ compareStats.mergeAmount || 0 }}</div>
            </div>
          </div>
        </div>

        <!-- 带升数据 -->
        <div class="bg-white rounded-lg p-4 shadow-sm border">
          <div class="text-sm text-gray-500 mb-2">带升日</div>
          <div class="grid grid-cols-3 gap-2 text-center">
            <div>
              <div class="text-xs text-gray-400">并升日</div>
              <div class="text-lg font-bold">{{ pendingStats.dayPending || '-' }}</div>
            </div>
            <div>
              <div class="text-xs text-gray-400">派升日</div>
              <div class="text-lg font-bold">{{ pendingStats.dispatchPending || '-' }}</div>
            </div>
            <div>
              <div class="text-xs text-gray-400">带升金额</div>
              <div class="text-lg font-bold text-orange-600">{{ pendingStats.pendingAmount || 0 }}</div>
            </div>
          </div>
        </div>

        <!-- 月度数据 -->
        <div class="bg-white rounded-lg p-4 shadow-sm border">
          <div class="text-sm text-gray-500 mb-2">月度数据</div>
          <div class="grid grid-cols-3 gap-2 text-center">
            <div>
              <div class="text-xs text-gray-400">并升日</div>
              <div class="text-lg font-bold">{{ monthStats.monthCompare || '-' }}</div>
            </div>
            <div>
              <div class="text-xs text-gray-400">派升日</div>
              <div class="text-lg font-bold">{{ monthStats.monthDispatch || '-' }}</div>
            </div>
            <div>
              <div class="text-xs text-gray-400">月度金额</div>
              <div class="text-lg font-bold text-green-600">{{ monthStats.monthAmount || 0 }}</div>
            </div>
          </div>
        </div>
      </div>
    </ContentWrap>

    <!-- 实时目标量 -->
    <ContentWrap>
      <div class="bg-white rounded-lg p-4 shadow-sm border mb-4">
        <div class="flex items-center justify-between mb-4">
          <h3 class="text-lg font-semibold">实时目标量</h3>
          <div class="flex items-center space-x-2">
            <Icon icon="ep:refresh" class="cursor-pointer text-blue-500" @click="refreshData" />
            <span class="text-sm text-gray-500">{{ lastUpdateTime }}</span>
          </div>
        </div>

        <!-- 标签切换 -->
        <div class="mb-4">
          <el-segmented v-model="activeTab" :options="tabOptions" class="w-full" />
        </div>

        <!-- 目标数据展示 -->
        <div class="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-8 gap-4">
          <div v-for="item in currentTabData" :key="item.label" class="text-center">
            <div class="text-xs text-gray-400 mb-1">{{ item.label }}</div>
            <div class="text-lg font-bold" :class="item.color">{{ item.value }}</div>
            <div class="text-xs text-gray-500">{{ item.unit }}</div>
          </div>
        </div>
      </div>
    </ContentWrap>

    <!-- 数据表格 -->
    <ContentWrap>
      <div class="bg-white rounded-lg shadow-sm border">
        <div class="p-4 border-b">
          <h3 class="text-lg font-semibold">门店运营数据</h3>
        </div>

        <el-table v-loading="tableLoading" :data="tableData" stripe style="width: 100%" :show-overflow-tooltip="true">
          <el-table-column prop="platform" label="外卖平台" width="120" align="center" />
          <el-table-column label="总单量" width="100" align="center">
            <template #default="scope">
              <span class="font-semibold">{{ scope.row.totalOrders }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="dayCompare" label="并比" width="80" align="center" />
          <el-table-column prop="yearCompare" label="同比" width="80" align="center" />

          <el-table-column prop="operationDept" label="运营部门" width="120" align="center" />
          <el-table-column label="总单量" width="100" align="center">
            <template #default="scope">
              <span class="font-semibold">{{ scope.row.operationOrders }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="operationDayCompare" label="并比" width="80" align="center" />
          <el-table-column prop="operationYearCompare" label="同比" width="80" align="center" />

          <el-table-column prop="businessDept" label="招商部门" width="120" align="center" />
          <el-table-column label="总单量" width="100" align="center">
            <template #default="scope">
              <span class="font-semibold">{{ scope.row.businessOrders }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="businessDayCompare" label="并比" width="80" align="center" />
          <el-table-column prop="businessYearCompare" label="同比" width="80" align="center" />
        </el-table>

        <!-- 分页 -->
        <div class="p-4">
          <Pagination :total="total" v-model:page="queryParams.pageNo" v-model:limit="queryParams.pageSize"
            @pagination="getTableData" />
        </div>
      </div>
    </ContentWrap>
  </div>
</template>

<script setup lang="ts">
import { dateFormatter } from '@/utils/formatTime'
import { DashboardApi, type DashboardStatsVO, type CompareStatsVO, type PendingStatsVO, type MonthStatsVO, type TargetDataVO, type TableDataVO } from '@/api/system/dashboard'

/** 数据统计仪表板 */
defineOptions({ name: 'SystemDashboard' })

const message = useMessage() // 消息弹窗
const { t } = useI18n() // 国际化

// 响应式数据
const tableLoading = ref(false)
const statsLoading = ref(false)
const activeTab = ref('operation')
const lastUpdateTime = ref('')

// 统计数据
const todayStats = ref<DashboardStatsVO>({
  orderCount: 0,
  dispatchCount: 0,
  completedCount: 0,
  totalAmount: 0
})

const compareStats = ref<CompareStatsVO>({
  dayCompare: '-',
  dispatchCompare: '-',
  mergeAmount: 0
})

const pendingStats = ref<PendingStatsVO>({
  dayPending: '-',
  dispatchPending: '-',
  pendingAmount: 0
})

const monthStats = ref<MonthStatsVO>({
  monthCompare: '-',
  monthDispatch: '-',
  monthAmount: 0
})

// 标签选项
const tabOptions = [
  { label: '运营数据', value: 'operation' },
  { label: '品牌数据', value: 'brand' }
]

// 目标数据
const operationData = ref<TargetDataVO[]>([
  { label: '运营', value: '总单量', unit: '', color: 'text-blue-600' },
  { label: '总单量/并比', value: '并比', unit: '', color: 'text-green-600' },
  { label: '并比', value: '同比', unit: '', color: 'text-orange-600' },
  { label: '同比', value: '美团单量', unit: '', color: 'text-purple-600' },
  { label: '美团单量', value: '并比', unit: '', color: 'text-red-600' },
  { label: '并比', value: '同比', unit: '', color: 'text-indigo-600' },
  { label: '同比', value: '目标单量', unit: '', color: 'text-pink-600' },
  { label: '目标单量', value: '完成率', unit: '', color: 'text-teal-600' }
])

const brandData = ref<TargetDataVO[]>([
  { label: '品牌', value: '总单量', unit: '', color: 'text-blue-600' },
  { label: '总单量', value: '并比', unit: '', color: 'text-green-600' },
  { label: '并比', value: '同比', unit: '', color: 'text-orange-600' },
  { label: '同比', value: '品牌单量', unit: '', color: 'text-purple-600' },
  { label: '品牌单量', value: '并比', unit: '', color: 'text-red-600' },
  { label: '并比', value: '同比', unit: '', color: 'text-indigo-600' },
  { label: '同比', value: '目标完成', unit: '', color: 'text-pink-600' },
  { label: '目标完成', value: '完成率', unit: '', color: 'text-teal-600' }
])

// 当前标签数据
const currentTabData = computed(() => {
  return activeTab.value === 'operation' ? operationData.value : brandData.value
})

// 表格数据
const tableData = ref<TableDataVO[]>([])
const total = ref(0)
const queryParams = reactive({
  pageNo: 1,
  pageSize: 10
})

/** 获取表格数据 */
const getTableData = async () => {
  tableLoading.value = true
  try {
    // 模拟数据
    const mockData = [
      {
        platform: '外卖平台',
        totalOrders: '总单量',
        dayCompare: '并比',
        yearCompare: '同比',
        operationDept: '运营部门',
        operationOrders: '总单量',
        operationDayCompare: '并比',
        operationYearCompare: '同比',
        businessDept: '招商部门',
        businessOrders: '总单量',
        businessDayCompare: '并比',
        businessYearCompare: '同比'
      },
      {
        platform: '招商部门',
        totalOrders: '总单量',
        dayCompare: '并比',
        yearCompare: '同比',
        operationDept: '运营部门',
        operationOrders: '总单量',
        operationDayCompare: '并比',
        operationYearCompare: '同比',
        businessDept: '招商部门',
        businessOrders: '总单量',
        businessDayCompare: '并比',
        businessYearCompare: '同比'
      },
      {
        platform: '招商部门',
        totalOrders: '总单量',
        dayCompare: '并比',
        yearCompare: '同比',
        operationDept: '运营部门',
        operationOrders: '总单量',
        operationDayCompare: '并比',
        operationYearCompare: '同比',
        businessDept: '招商部门',
        businessOrders: '总单量',
        businessDayCompare: '并比',
        businessYearCompare: '同比'
      }
    ]

    tableData.value = mockData
    total.value = mockData.length
  } finally {
    tableLoading.value = false
  }
}

/** 刷新数据 */
const refreshData = () => {
  lastUpdateTime.value = dateFormatter(new Date())
  getTableData()
  message.success('数据已刷新')
}

/** 初始化 */
onMounted(() => {
  lastUpdateTime.value = dateFormatter(new Date())
  getTableData()
})
</script>

<style lang="scss" scoped>
.dashboard-container {
  padding: 16px;
}

.grid {
  display: grid;
}

.grid-cols-1 {
  grid-template-columns: repeat(1, minmax(0, 1fr));
}

.grid-cols-2 {
  grid-template-columns: repeat(2, minmax(0, 1fr));
}

.grid-cols-3 {
  grid-template-columns: repeat(3, minmax(0, 1fr));
}

.grid-cols-4 {
  grid-template-columns: repeat(4, minmax(0, 1fr));
}

.grid-cols-8 {
  grid-template-columns: repeat(8, minmax(0, 1fr));
}

@media (min-width: 768px) {
  .md\:grid-cols-2 {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }

  .md\:grid-cols-4 {
    grid-template-columns: repeat(4, minmax(0, 1fr));
  }
}

@media (min-width: 1024px) {
  .lg\:grid-cols-4 {
    grid-template-columns: repeat(4, minmax(0, 1fr));
  }

  .lg\:grid-cols-8 {
    grid-template-columns: repeat(8, minmax(0, 1fr));
  }
}

.gap-2 {
  gap: 0.5rem;
}

.gap-4 {
  gap: 1rem;
}

.text-center {
  text-align: center;
}

.font-bold {
  font-weight: 700;
}

.font-semibold {
  font-weight: 600;
}

.text-xs {
  font-size: 0.75rem;
  line-height: 1rem;
}

.text-sm {
  font-size: 0.875rem;
  line-height: 1.25rem;
}

.text-lg {
  font-size: 1.125rem;
  line-height: 1.75rem;
}

.text-blue-600 {
  color: #2563eb;
}

.text-green-600 {
  color: #16a34a;
}

.text-orange-600 {
  color: #ea580c;
}

.text-purple-600 {
  color: #9333ea;
}

.text-red-600 {
  color: #dc2626;
}

.text-indigo-600 {
  color: #4f46e5;
}

.text-pink-600 {
  color: #db2777;
}

.text-teal-600 {
  color: #0d9488;
}

.text-gray-400 {
  color: #9ca3af;
}

.text-gray-500 {
  color: #6b7280;
}
</style>
